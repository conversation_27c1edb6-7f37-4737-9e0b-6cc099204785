"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getPreloadedDatabaseConfigs, preloadAppData, isPreloadingData } from "@/lib/preloader";
import { getDatabaseConfigs } from "@/lib/permissions";

export default function LoadingDebugPage() {
  const [preloadStatus, setPreloadStatus] = useState<{
    isPreloading: boolean;
    hasPreloaded: boolean;
    configCount: number;
    lastUpdate: string;
  }>({
    isPreloading: false,
    hasPreloaded: false,
    configCount: 0,
    lastUpdate: new Date().toISOString()
  });

  const [apiTestResults, setApiTestResults] = useState<{
    dbHealth: any;
    configApi: any;
    directFetch: any;
  }>({
    dbHealth: null,
    configApi: null,
    directFetch: null
  });

  const [loading, setLoading] = useState(false);

  // 检查预加载状态
  const checkPreloadStatus = () => {
    const preloaded = getPreloadedDatabaseConfigs();
    setPreloadStatus({
      isPreloading: isPreloadingData(),
      hasPreloaded: !!preloaded,
      configCount: preloaded ? Object.keys(preloaded).length : 0,
      lastUpdate: new Date().toISOString()
    });
  };

  // 测试API性能
  const testApiPerformance = async () => {
    setLoading(true);
    const results: any = {
      dbHealth: null,
      configApi: null,
      directFetch: null
    };

    try {
      // 测试数据库健康检查
      const healthStart = performance.now();
      const healthResponse = await fetch('/api/health/database');
      const healthData = await healthResponse.json();
      results.dbHealth = {
        ...healthData,
        responseTime: `${(performance.now() - healthStart).toFixed(2)}ms`
      };

      // 测试配置API
      const configStart = performance.now();
      const configResponse = await fetch('/api/config/databases');
      const configData = await configResponse.json();
      results.configApi = {
        success: configData.success,
        configCount: configData.data ? Object.keys(configData.data).length : 0,
        responseTime: `${(performance.now() - configStart).toFixed(2)}ms`
      };

      // 测试直接获取
      const directStart = performance.now();
      const directData = await getDatabaseConfigs();
      results.directFetch = {
        configCount: Object.keys(directData).length,
        responseTime: `${(performance.now() - directStart).toFixed(2)}ms`
      };

    } catch (error) {
      console.error('API test failed:', error);
    }

    setApiTestResults(results);
    setLoading(false);
  };

  // 触发预加载
  const triggerPreload = async () => {
    setLoading(true);
    try {
      await preloadAppData();
      checkPreloadStatus();
    } catch (error) {
      console.error('Preload failed:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    checkPreloadStatus();
    const interval = setInterval(checkPreloadStatus, 1000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Loading Performance Debug
          </h1>
          <p className="text-gray-600">
            Debug tool for analyzing database configuration loading performance
          </p>
        </div>

        {/* 预加载状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Preload Status
              <Button onClick={checkPreloadStatus} variant="outline" size="sm">
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-600">Is Preloading</p>
                <Badge variant={preloadStatus.isPreloading ? "default" : "secondary"}>
                  {preloadStatus.isPreloading ? "Yes" : "No"}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-gray-600">Has Preloaded</p>
                <Badge variant={preloadStatus.hasPreloaded ? "default" : "destructive"}>
                  {preloadStatus.hasPreloaded ? "Yes" : "No"}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-gray-600">Config Count</p>
                <Badge variant="outline">{preloadStatus.configCount}</Badge>
              </div>
              <div>
                <p className="text-sm text-gray-600">Last Update</p>
                <p className="text-xs text-gray-500">
                  {new Date(preloadStatus.lastUpdate).toLocaleTimeString()}
                </p>
              </div>
            </div>
            <Button onClick={triggerPreload} disabled={loading}>
              {loading ? "Loading..." : "Trigger Preload"}
            </Button>
          </CardContent>
        </Card>

        {/* API性能测试 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              API Performance Test
              <Button onClick={testApiPerformance} disabled={loading}>
                {loading ? "Testing..." : "Run Tests"}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 数据库健康检查 */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">Database Health</h4>
                {apiTestResults.dbHealth ? (
                  <div className="space-y-2 text-sm">
                    <p>Status: <Badge variant={apiTestResults.dbHealth.success ? "default" : "destructive"}>
                      {apiTestResults.dbHealth.status}
                    </Badge></p>
                    <p>Response Time: {apiTestResults.dbHealth.responseTime}</p>
                    {apiTestResults.dbHealth.checks?.configTable && (
                      <p>Config Count: {apiTestResults.dbHealth.checks.configTable.count}</p>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No data</p>
                )}
              </div>

              {/* 配置API */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">Config API</h4>
                {apiTestResults.configApi ? (
                  <div className="space-y-2 text-sm">
                    <p>Success: <Badge variant={apiTestResults.configApi.success ? "default" : "destructive"}>
                      {apiTestResults.configApi.success ? "Yes" : "No"}
                    </Badge></p>
                    <p>Response Time: {apiTestResults.configApi.responseTime}</p>
                    <p>Config Count: {apiTestResults.configApi.configCount}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No data</p>
                )}
              </div>

              {/* 直接获取 */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium mb-2">Direct Fetch</h4>
                {apiTestResults.directFetch ? (
                  <div className="space-y-2 text-sm">
                    <p>Response Time: {apiTestResults.directFetch.responseTime}</p>
                    <p>Config Count: {apiTestResults.directFetch.configCount}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No data</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>Optimization Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm space-y-2">
              <h4 className="font-medium">Applied Optimizations:</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>Added preloader that starts loading database configs on app initialization</li>
                <li>Implemented concurrent request deduplication in API routes</li>
                <li>Enhanced caching mechanisms with proper error handling</li>
                <li>Added database health check endpoint for diagnostics</li>
                <li>Improved loading states with better user feedback</li>
                <li>Added fallback mechanisms for failed preloads</li>
              </ul>
            </div>
            <div className="text-sm space-y-2">
              <h4 className="font-medium">Expected Results:</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>Faster initial page load (especially after server restart)</li>
                <li>Reduced "Loading..." time on homepage and navigation</li>
                <li>Better error handling and user feedback</li>
                <li>Improved database connection management</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
