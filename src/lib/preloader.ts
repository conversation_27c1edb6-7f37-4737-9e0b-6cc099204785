/**
 * 应用预加载器 - 在应用启动时预加载关键数据
 * 解决首页和导航栏首次加载缓慢的问题
 */

import { getDatabaseConfigs } from "@/lib/permissions";

// 全局预加载状态
let isPreloading = false;
let preloadPromise: Promise<void> | null = null;
let preloadedData: {
  databaseConfigs?: Record<string, any>;
} = {};

/**
 * 预加载关键应用数据
 */
export async function preloadAppData(): Promise<void> {
  // 如果已经在预加载中，返回现有的Promise
  if (preloadPromise) {
    return preloadPromise;
  }

  // 如果已经预加载完成，直接返回
  if (preloadedData.databaseConfigs) {
    return Promise.resolve();
  }

  isPreloading = true;
  
  preloadPromise = (async () => {
    try {
      console.log('[Preloader] 开始预加载应用数据...');
      
      // 预加载数据库配置
      const configs = await getDatabaseConfigs();
      preloadedData.databaseConfigs = configs;
      
      console.log(`[Preloader] 预加载完成，获取到 ${Object.keys(configs).length} 个数据库配置`);
      
    } catch (error) {
      console.error('[Preloader] 预加载失败:', error);
      // 预加载失败不应该阻塞应用启动
      preloadedData.databaseConfigs = {};
    } finally {
      isPreloading = false;
      preloadPromise = null;
    }
  })();

  return preloadPromise;
}

/**
 * 获取预加载的数据库配置
 */
export function getPreloadedDatabaseConfigs(): Record<string, any> | null {
  return preloadedData.databaseConfigs || null;
}

/**
 * 检查是否正在预加载
 */
export function isPreloadingData(): boolean {
  return isPreloading;
}

/**
 * 清除预加载缓存（用于开发环境热重载）
 */
export function clearPreloadCache(): void {
  preloadedData = {};
  preloadPromise = null;
  isPreloading = false;
}

/**
 * 在浏览器环境中自动开始预加载
 */
if (typeof window !== 'undefined') {
  // 延迟一点时间开始预加载，避免阻塞首屏渲染
  setTimeout(() => {
    preloadAppData().catch(error => {
      console.warn('[Preloader] 自动预加载失败:', error);
    });
  }, 100);
}
